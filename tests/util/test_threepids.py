#
# This file is licensed under the Affero General Public License (AGPL) version 3.
#
# Copyright 2020 <PERSON>
# Copyright (C) 2023 New Vector, Ltd
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as
# published by the Free Software Foundation, either version 3 of the
# License, or (at your option) any later version.
#
# See the GNU Affero General Public License for more details:
# <https://www.gnu.org/licenses/agpl-3.0.html>.
#
# Originally licensed under the Apache License, Version 2.0:
# <http://www.apache.org/licenses/LICENSE-2.0>.
#
# [This file includes modifications made by New Vector Limited]
#
#

from synapse.util.threepids import canonicalise_email

from tests.unittest import HomeserverTestCase


class CanonicaliseEmailTests(HomeserverTestCase):
    def test_no_at(self) -> None:
        with self.assertRaises(ValueError):
            canonicalise_email("address-without-at.bar")

    def test_two_at(self) -> None:
        with self.assertRaises(ValueError):
            canonicalise_email("foo@<EMAIL>")

    def test_bad_format(self) -> None:
        with self.assertRaises(ValueError):
            canonicalise_email("<EMAIL>@good.example.com")

    def test_valid_format(self) -> None:
        self.assertEqual(canonicalise_email("<EMAIL>"), "<EMAIL>")

    def test_domain_to_lower(self) -> None:
        self.assertEqual(canonicalise_email("<EMAIL>"), "<EMAIL>")

    def test_domain_with_umlaut(self) -> None:
        self.assertEqual(canonicalise_email("foo@Öumlaut.com"), "foo@öumlaut.com")

    def test_address_casefold(self) -> None:
        self.assertEqual(
            canonicalise_email("Strauß@Example.com"), "<EMAIL>"
        )

    def test_address_trim(self) -> None:
        self.assertEqual(canonicalise_email(" <EMAIL> "), "<EMAIL>")
