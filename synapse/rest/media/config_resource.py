#
# This file is licensed under the Affero General Public License (AGPL) version 3.
#
# Copyright 2020-2021 The Matrix.org Foundation C.I.C.
# Copyright 2018 Will <PERSON> <<EMAIL>>
# Copyright (C) 2023 New Vector, Ltd
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as
# published by the Free Software Foundation, either version 3 of the
# License, or (at your option) any later version.
#
# See the GNU Affero General Public License for more details:
# <https://www.gnu.org/licenses/agpl-3.0.html>.
#
# Originally licensed under the Apache License, Version 2.0:
# <http://www.apache.org/licenses/LICENSE-2.0>.
#
# [This file includes modifications made by New Vector Limited]
#
#

import re
from typing import TYPE_CHECKING

from synapse.http.server import respond_with_json
from synapse.http.servlet import RestServlet
from synapse.http.site import SynapseRequest

if TYPE_CHECKING:
    from synapse.server import HomeServer


class MediaConfigResource(RestServlet):
    PATTERNS = [re.compile("/_matrix/media/(r0|v3|v1)/config$")]

    def __init__(self, hs: "HomeServer"):
        super().__init__()
        config = hs.config
        self.clock = hs.get_clock()
        self.auth = hs.get_auth()
        self.limits_dict = {"m.upload.size": config.media.max_upload_size}
        self.media_repository_callbacks = hs.get_module_api_callbacks().media_repository

    async def on_GET(self, request: SynapseRequest) -> None:
        requester = await self.auth.get_user_by_req(request)
        user_specific_config = (
            await self.media_repository_callbacks.get_media_config_for_user(
                requester.user.to_string()
            )
        )
        response = user_specific_config if user_specific_config else self.limits_dict
        respond_with_json(request, 200, response, send_cors=True)
