{% extends "_base.html" %}
{% block title %}Choose identity provider{% endblock %}

{% block header %}
<style type="text/css">
  {% include "sso.css" without context %}

  .providers {
    list-style: none;
    padding: 0;
  }

  .providers li {
    margin: 12px;
  }

  .providers a {
    display: block;
    border-radius: 4px;
    border: 1px solid #17191C;
    padding: 8px;
    text-align: center;
    text-decoration: none;
    color: #17191C;
    display: flex;
    align-items: center;
    font-weight: bold;
  }

  .providers a img {
    width: 24px;
    height: 24px;
  }
  .providers a span {
    flex: 1;
  }
</style>
{% endblock %}

{% block body %}
<header>
    <h1>Log in to {{ server_name }} </h1>
    <p>Choose an identity provider to log in</p>
</header>
<main>
    <ul class="providers">
        {% for p in providers %}
        <li>
            <a href="pick_idp?idp={{ p.idp_id }}&redirectUrl={{ redirect_url | urlencode }}">
                {% if p.idp_icon %}
                <img src="{{ p.idp_icon | mxc_to_http(32, 32) }}"/>
                {% endif %}
                <span>{{ p.idp_name }}</span>
            </a>
        </li>
        {% endfor %}
    </ul>
</main>
{% include "sso_footer.html" without context %}
{% endblock %}
