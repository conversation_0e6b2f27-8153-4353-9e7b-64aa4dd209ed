{% extends "_base.html" %}
{% block title %}Authentication{% endblock %}

{% block header %}
<script src="https://www.recaptcha.net/recaptcha/api.js" async defer></script>
<style type="text/css">
  .g-recaptcha div {
    margin: auto;
  }
</style>
<script>
function captchaDone() {
    document.getElementById('registrationForm').submit(); 
}
</script>
{% endblock %}

{% block body %}
<form id="registrationForm" method="post" action="{{ myurl }}">
    <div>
        {% if error is defined %}
            <p class="error"><strong>Error: {{ error }}</strong></p>
        {% endif %}
        <p>
        Hello! We need to prevent computer programs and other automated
        things from creating accounts on this server.
        </p>
        <p>
        Please verify that you're not a robot.
        </p>
        <input type="hidden" name="session" value="{{ session }}" />
        <div class="g-recaptcha"
            data-sitekey="{{ sitekey }}"
            data-callback="captchaDone">
        </div>
        <noscript>
        <input type="submit" value="All Done" />
        </noscript>
        </div>
    </div>
</form>
{% endblock %}