{%- for message in notif.messages %}
{%- if message.event_type == "m.room.encrypted" %}
An encrypted message.
{%- elif message.event_type == "m.room.message" %}
{%- if message.msgtype == "m.emote" %}* {%- endif %}{{ message.sender_name }} ({{ message.ts|format_ts("%H:%M") }})
{%- if message.msgtype == "m.text" %}
{{ message.body_text_plain }}
{%- elif message.msgtype == "m.emote" %}
{{ message.body_text_plain }}
{%- elif message.msgtype == "m.notice" %}
{{ message.body_text_plain }}
{%- elif message.msgtype == "m.image" %}
{{ message.body_text_plain }}
{%- elif message.msgtype == "m.file" %}
{{ message.body_text_plain }}
{%- else %}
A message with unrecognised content.
{%- endif %}
{%- endif %}
{%- endfor %}

View {{ room.title }} at {{ notif.link }}
