body {
    margin: 0px;
}

pre, code {
    word-break: break-word;
    white-space: pre-wrap;
}

#page {
    font-family: 'Open Sans', Helvetica, Arial, Sans-Serif;
    font-color: #454545;
    font-size: 12pt;
    width: 100%;
    padding: 20px;
}

#inner {
    width: 640px;
}

.header {
    width: 100%;
    height: 87px;
    color: #454545;
    border-bottom: 4px solid #e5e5e5;
}

.logo {
    text-align: right;
    margin-left: 20px;
}

.salutation {
    padding-top: 10px;
    font-weight: bold;
}

.summarytext {
}

.room {
    width: 100%;
    color: #454545;
    border-bottom: 1px solid #e5e5e5;
}

.room_header td {
    padding-top: 38px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
}

.room_name {
    vertical-align: middle;
    font-size: 18px;
    font-weight: bold;
}

.room_header h2 {
    margin-top: 0px;
    margin-left: 75px;
    font-size: 20px;
}

.room_avatar {
    width: 56px;
    line-height: 0px;
    text-align: center;
    vertical-align: middle;
}

.room_avatar img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 24px;
}

.notif {
    border-bottom: 1px solid #e5e5e5;
    margin-top: 16px;
    padding-bottom: 16px;
}

.historical_message .sender_avatar {
    opacity: 0.3;
}

/* spell out opacity and historical_message class names for Outlook aka Word */
.historical_message .sender_name {
    color: #e3e3e3;
}

.historical_message .message_time {
    color: #e3e3e3;
}

.historical_message .message_body {
    color: #c7c7c7;
}

.historical_message td,
.message td {
    padding-top: 10px;
}

.sender_avatar {
    width: 56px;
    text-align: center;
    vertical-align: top;
}

.sender_avatar img {
    margin-top: -2px;
    width: 32px;
    height: 32px;
    border-radius: 16px;
}

.sender_name  {
    display: inline;
    font-size: 13px;
    color: #a2a2a2;
}

.message_time  {
    text-align: right;
    width: 100px;
    font-size: 11px;
    color: #a2a2a2;
}

.message_body {
}

.notif_link td {
    padding-top: 10px;
    padding-bottom: 10px;
    font-weight: bold;
}

.notif_link a, .footer a {
    color: #454545;
    text-decoration: none;
}

.debug {
    font-size: 10px;
    color: #888;
}

.footer {
    margin-top: 20px;
    text-align: center;
}