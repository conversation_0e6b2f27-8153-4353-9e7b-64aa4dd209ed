{% extends "_base.html" %}

{% block title %}New activity in room{% endblock %}

{% block header %}
<style type="text/css">
    {%- include 'mail.css' without context %}
    {%- include "mail-%s.css" % app_name ignore missing without context %}
</style>
{% endblock %}

{% block body %}
<table id="page">
    <tr>
        <td> </td>
        <td id="inner">
            <table class="header">
                <tr>
                    <td>
                        <div class="salutation">Hi {{ user_display_name }},</div>
                        <div class="summarytext">{{ summary_text }}</div>
                    </td>
                    <td class="logo">
                        {%- if app_name == "Riot" %}
                            <img src="https://riot.im/img/external/riot-logo-email.png" width="83" height="83" alt="[Riot]"/>
                        {%- elif app_name == "Vector" %}
                            <img src="https://matrix.org/img/vector-logo-email.png" width="64" height="83" alt="[Vector]"/>
                        {%- elif app_name == "Element" %}
                            <img src="https://static.element.io/images/email-logo.png" width="83" height="83" alt="[Element]"/>
                        {%- else %}
                            <img src="https://matrix.org/img/matrix-120x51.png" width="120" height="51" alt="[matrix]"/>
                        {%- endif %}
                    </td>
                </tr>
            </table>
            {%- for room in rooms %}
                {%- include 'room.html' with context %}
            {%- endfor %}
            <div class="footer">
                <a href="{{ unsubscribe_link }}">Unsubscribe</a>
                <br/>
                <br/>
                <div class="debug">
                    Sending email at {{ reason.now|format_ts("%c") }} due to activity in room {{ reason.room_name }} because
                    an event was received at {{ reason.received_at|format_ts("%c") }}
                    which is more than {{ "%.1f"|format(reason.delay_before_mail_ms / (60*1000)) }} ({{ reason.delay_before_mail_ms }}) mins ago,
                    {%- if reason.last_sent_ts %}
                        and the last time we sent a mail for this room was {{ reason.last_sent_ts|format_ts("%c") }},
                        which is more than {{ "%.1f"|format(reason.throttle_ms / (60*1000)) }} (current throttle_ms) mins ago.
                    {%- else %}
                        and we don't have a last time we sent a mail for this room.
                    {%- endif %}
                </div>
            </div>
        </td>
        <td> </td>
    </tr>
</table>
{% endblock %}
