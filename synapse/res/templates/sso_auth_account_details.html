{% extends "_base.html" %}
{% block title %}Create your account{% endblock %}

{% block header %}
<script type="text/javascript">
  let wasKeyboard = false;
  document.addEventListener("mousedown", function() { wasKeyboard = false; });
  document.addEventListener("keydown", function() { wasKeyboard = true; });
  document.addEventListener("focusin", function() {
    if (wasKeyboard) {
      document.body.classList.add("keyboard-focus");
    } else {
      document.body.classList.remove("keyboard-focus");
    }
  });
</script>
<style type="text/css">
  {% include "sso.css" without context %}

  body.keyboard-focus :focus, body.keyboard-focus .username_input:focus-within {
    outline: 3px solid #17191C;
    outline-offset: 4px;
  }

  .username_input {
    display: flex;
    border: 2px solid #418DED;
    border-radius: 8px;
    padding: 12px;
    position: relative;
    margin: 16px 0;
    align-items: center;
    font-size: 12px;
  }

  .username_input.invalid {
    border-color: #FE2928;
  }

  .username_input.invalid input, .username_input.invalid label {
    color: #FE2928;
  }

  .username_input div, .username_input input {
    line-height: 18px;
    font-size: 14px;
  }

  .username_input label {
    position: absolute;
    top: -5px;
    left: 14px;
    font-size: 10px;
    line-height: 10px;
    background: white;
    padding: 0 2px;
  }

  .username_input input {
    flex: 1;
    display: block;
    min-width: 0;
    border: none;
  }

  /* only clear the outline if we know it will be shown on the parent div using :focus-within */
  @supports selector(:focus-within) {
    .username_input input {
      outline: none !important;
    }
  }

  .username_input div {
    color: #8D99A5;
  }

  .idp-pick-details {
    border: 1px solid #E9ECF1;
    border-radius: 8px;
    margin: 24px 0;
  }

  .idp-pick-details h2 {
    margin: 0;
    padding: 8px 12px;
  }

  .idp-pick-details .idp-detail {
    border-top: 1px solid #E9ECF1;
    padding: 12px;
    display: block;
  }
  .idp-pick-details .check-row {
    display: flex;
    align-items: center;
  }

  .idp-pick-details .check-row .name {
    flex: 1;
  }

  .idp-pick-details .use, .idp-pick-details .idp-value {
    color: #737D8C;
  }

  .idp-pick-details .idp-value {
    margin: 0;
    margin-top: 8px;
  }

  .idp-pick-details .avatar {
    width: 53px;
    height: 53px;
    border-radius: 100%;
    display: block;
    margin-top: 8px;
  }

  output {
    padding: 0 14px;
    display: block;
  }

  output.error {
    color: #FE2928;
  }
</style>
{% endblock %}

{% block body %}
<header>
  <h1>Create your account</h1>
  <p>This is required. Continue to create your account on {{ server_name }}. You can't change this later.</p>
</header>
<main>
  <form method="post" class="form__input" id="form">
    <div class="username_input" id="username_input">
      <label for="field-username">Username (required)</label>
      <div class="prefix">@</div>
      <input type="text" name="username" id="field-username" value="{{ user_attributes.localpart }}" autofocus autocorrect="off" autocapitalize="none">
      <div class="postfix">:{{ server_name }}</div>
    </div>
    <output for="username_input" id="field-username-output"></output>
    <input type="submit" value="Continue" class="primary-button">
    {% if user_attributes.avatar_url or user_attributes.display_name or user_attributes.emails %}
    <section class="idp-pick-details">
      <h2>{% if idp.idp_icon %}<img src="{{ idp.idp_icon | mxc_to_http(24, 24) }}"/>{% endif %}Optional data from {{ idp.idp_name }}</h2>
      {% if user_attributes.avatar_url %}
      <label class="idp-detail idp-avatar" for="idp-avatar">
        <div class="check-row">
          <span class="name">Avatar</span>
          <span class="use">Use</span>
          <input type="checkbox" name="use_avatar" id="idp-avatar" value="true" checked>
        </div>
        <img src="{{ user_attributes.avatar_url }}" class="avatar" />
      </label>
      {% endif %}
      {% if user_attributes.display_name %}
      <label class="idp-detail" for="idp-displayname">
        <div class="check-row">
          <span class="name">Display name</span>
          <span class="use">Use</span>
          <input type="checkbox" name="use_display_name" id="idp-displayname" value="true" checked>
        </div>
        <p class="idp-value">{{ user_attributes.display_name }}</p>
      </label>
      {% endif %}
      {% for email in user_attributes.emails %}
      <label class="idp-detail" for="idp-email{{ loop.index }}">
        <div class="check-row">
          <span class="name">E-mail</span>
          <span class="use">Use</span>
          <input type="checkbox" name="use_email" id="idp-email{{ loop.index }}" value="{{ email }}" checked>
        </div>
        <p class="idp-value">{{ email }}</p>
      </label>
      {% endfor %}
    </section>
    {% endif %}
  </form>
</main>
{% include "sso_footer.html" without context %}
<script type="text/javascript">
  {% include "sso_auth_account_details.js" without context %}
</script>
{% endblock %}
