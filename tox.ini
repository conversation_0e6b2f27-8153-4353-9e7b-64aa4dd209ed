[tox]
envlist = py39, py310, py311, py312, py313

# we require tox>=2.3.2 for the fix to https://github.com/tox-dev/tox/issues/208
minversion = 2.3.2

# the tox-venv plugin makes tox use python's built-in `venv` module rather than
# the legacy `virtualenv` tool. `virtualenv` embeds its own `pip`, `setuptools`,
# etc, and ends up being rather unreliable.
requires = tox-venv

[base]
deps =
    python-subunit
    junitxml
    coverage

    # this is pinned since it's a bit of an obscure package.
    coverage-enable-subprocess==1.0

    # cyptography 2.2 requires setuptools >= 18.5
    #
    # older versions of virtualenv (?) give us a virtualenv with the same
    # version of setuptools as is installed on the system python (and tox runs
    # virtualenv under python3, so we get the version of setuptools that is
    # installed on that).
    #
    # anyway, make sure that we have a recent enough setuptools.
    setuptools>=18.5

    # we also need a semi-recent version of pip, because old ones fail to
    # install the "enum34" dependency of cryptography.
    pip>=10

# default settings for all tox environments
[testenv]
deps =
    {[base]deps}
extras =
    # install the optional dependendencies for tox environments without
    # '-noextras' in their name
    # (this requires tox 3)
    !noextras: all
    test

setenv =
    # use a postgres db for tox environments with "-postgres" in the name
    # (see https://tox.readthedocs.io/en/3.20.1/config.html#factors-and-factor-conditional-settings)
    postgres: SYNAPSE_POSTGRES = 1

    # this is used by .coveragerc to refer to the top of our tree.
    TOP={toxinidir}

passenv = *

commands =
    # the "env" invocation enables coverage checking for sub-processes. This is
    # particularly important when running trial with `-j`, since that will make
    # it run tests in a subprocess, whose coverage would otherwise not be
    # tracked.  (It also makes an explicit `coverage run` command redundant.)
    #
    # (See https://coverage.readthedocs.io/en/coverage-5.3/subprocess.html.
    # Note that the `coverage.process_startup()` call is done by
    # `coverage-enable-subprocess`.)
    #
    # we use "env" rather than putting a value in `setenv` so that it is not
    # inherited by other tox environments.
    #
    /usr/bin/env COVERAGE_PROCESS_START={toxinidir}/.coveragerc "{envbindir}/trial" {env:TRIAL_FLAGS:} {posargs:tests} {env:TOXSUFFIX:}

# As of twisted 16.4, trial tries to import the tests as a package (previously
# it loaded the files explicitly), which means they need to be on the
# pythonpath. Our sdist doesn't include the 'tests' package, so normally it
# doesn't work within the tox virtualenv.
#
# As a workaround, we tell tox to do install with 'pip -e', which just
# creates a symlink to the project directory instead of unpacking the sdist.
#
# (An alternative to this would be to set PYTHONPATH to include the project
# directory. Note two problems with this:
#
#   - if you set it via `setenv`, then it is also set during the 'install'
#     phase, which inhibits unpacking the sdist, so the virtualenv isn't
#     useful for anything else without setting PYTHONPATH similarly.
#
#   - `synapse` is also loaded from PYTHONPATH so even if you only set
#     PYTHONPATH for the test phase, we're still running the tests against
#     the working copy rather than the contents of the sdist. So frankly
#     you might as well use -e in the first place.
#
# )
usedevelop=true



[testenv:benchmark]
deps =
    {[base]deps}
    pyperf
setenv =
    SYNAPSE_POSTGRES = 1
commands =
    python -m synmark {posargs:}

