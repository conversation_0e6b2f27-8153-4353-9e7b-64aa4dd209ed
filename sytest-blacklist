# This file serves as a blacklist for SyTest tests that we expect will fail in
# Synapse. This doesn't include flakey tests---better to deflake them instead.
#
# Each line of this file is scanned by sytest during a run and if the line
# exactly matches the name of a test, it will be marked as "expected fail",
# meaning the test will still run, but failure will not mark the entire test
# suite as failing.
#
# Test names are encouraged to have a bug accompanied with them, serving as an
# explanation for why the test has been excluded.

# Blacklisted due to https://github.com/matrix-org/synapse/issues/2065
Guest users can accept invites to private rooms over federation

# Blacklisted due to https://github.com/vector-im/riot-web/issues/7211
The only membership state included in a gapped incremental sync is for senders in the timeline

# Blacklisted due to https://github.com/matrix-org/synapse/issues/1658
Newly created users see their own presence in /initialSync (SYT-34)

# Blacklisted due to https://github.com/matrix-org/synapse/issues/1396
Should reject keys claiming to belong to a different user

# Blacklisted until MSC2753 is implemented
Local users can peek into world_readable rooms by room ID
We can't peek into rooms with shared history_visibility
We can't peek into rooms with invited history_visibility
We can't peek into rooms with joined history_visibility
Local users can peek by room alias
Peeked rooms only turn up in the sync for the device who peeked them

# Validation needs to be added to Synapse: https://github.com/matrix-org/synapse/issues/10554
Rejects invalid device keys
